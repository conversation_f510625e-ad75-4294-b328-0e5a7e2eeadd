<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中文分词测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        h2 {
            color: #4285f4;
            border-bottom: 2px solid #4285f4;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #4285f4;
        }
        
        .chinese-text {
            font-size: 16px;
            line-height: 1.8;
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e0e0e0;
        }
        
        .mixed-text {
            font-size: 16px;
            line-height: 1.8;
            margin: 15px 0;
            padding: 15px;
            background: #fff3cd;
            border-radius: 5px;
            border: 1px solid #ffeaa7;
        }
        
        .english-text {
            font-size: 16px;
            line-height: 1.8;
            margin: 15px 0;
            padding: 15px;
            background: #e7f3ff;
            border-radius: 5px;
            border: 1px solid #b3d9ff;
        }
        
        .technical-text {
            font-size: 16px;
            line-height: 1.8;
            margin: 15px 0;
            padding: 15px;
            background: #f0f8f0;
            border-radius: 5px;
            border: 1px solid #c8e6c9;
        }
        
        .test-button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        
        .test-button:hover {
            background: #3367d6;
        }
        
        .description {
            color: #666;
            font-style: italic;
            margin-bottom: 10px;
        }
        
        .highlight {
            background: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .word-count {
            color: #888;
            font-size: 14px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔤 中文分词算法测试页面</h1>
        
        <div class="test-section">
            <h2>📝 纯中文文本测试</h2>
            <div class="description">测试中文词汇的准确分词，如"基本设置"应分为"基本"和"设置"</div>
            
            <div class="chinese-text">
                基本设置包含系统配置和用户管理功能。用户可以通过界面操作来修改各种参数，包括语言选择、主题设置、通知配置等重要选项。
            </div>
            
            <div class="chinese-text">
                数据管理系统提供文件上传、下载和编辑功能。支持多种格式的文档处理，确保信息安全和数据完整性。
            </div>
            
            <div class="chinese-text">
                网络连接状态良好时，翻译功能可以正常工作。自动翻译模式会智能识别页面内容并进行实时翻译处理。
            </div>
        </div>

        <div class="test-section">
            <h2>🌐 中英混合文本测试</h2>
            <div class="description">测试中英文混合内容的分词效果</div>
            
            <div class="mixed-text">
                Chrome浏览器支持JavaScript扩展程序开发。开发者可以使用HTML、CSS和JavaScript技术创建功能丰富的插件。
            </div>
            
            <div class="mixed-text">
                API接口调用需要配置正确的URL地址和Token验证。系统会自动处理HTTP请求并返回JSON格式的响应数据。
            </div>
            
            <div class="mixed-text">
                GitHub是全球最大的代码托管平台，支持Git版本控制。开发者可以在上面分享开源项目和协作开发。
            </div>
        </div>

        <div class="test-section">
            <h2>🔤 英文文本测试</h2>
            <div class="description">测试纯英文内容的单词分割</div>
            
            <div class="english-text">
                The advanced word segmentation algorithm provides accurate text processing capabilities for multiple languages including Chinese and English.
            </div>
            
            <div class="english-text">
                Machine learning models can improve translation quality through continuous training and optimization processes.
            </div>
            
            <div class="english-text">
                User interface design should focus on usability and accessibility to ensure a better user experience.
            </div>
        </div>

        <div class="test-section">
            <h2>⚙️ 技术术语测试</h2>
            <div class="description">测试包含技术术语和特殊格式的文本</div>
            
            <div class="technical-text">
                配置文件config.json包含API密钥和服务器地址。请确保api_key字段设置正确，端口号默认为8080。
            </div>
            
            <div class="technical-text">
                数据库连接字符串：mysql://username:password@localhost:3306/database_name。支持SSL加密连接。
            </div>
            
            <div class="technical-text">
                版本号v2.1.3已发布，修复了内存泄漏问题。下载地址：https://github.com/example/project/releases/tag/v2.1.3
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 特殊情况测试</h2>
            <div class="description">测试标点符号、数字、特殊字符的处理</div>

            <div class="chinese-text">
                价格：￥299.99（原价￥399.99），折扣率：75%。有效期至2024年12月31日。
            </div>

            <div class="chinese-text">
                联系方式：电话400-123-4567，邮箱*******************，QQ群：123456789。
            </div>

            <div class="chinese-text">
                "智能翻译"功能支持50+种语言，准确率达到95%以上。支持PDF、Word、Excel等格式。
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 问题文本测试</h2>
            <div class="description">测试您提到的具体问题文本</div>

            <div class="chinese-text" id="problem-text">
                因为是长期使用且存在多个Token交互的，所以采用密钥管理（KV即可）
            </div>

            <div class="chinese-text">
                由于系统需要处理大量数据，因此建议使用缓存机制来提高性能。
            </div>

            <div class="chinese-text">
                通过API接口可以实现自动化操作，包括用户认证、数据同步等功能。
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="test-button" onclick="testSegmentation()">🔍 测试分词效果</button>
            <button class="test-button" onclick="clearHighlights()">🧹 清除高亮</button>
            <button class="test-button" onclick="showStatistics()">📊 显示统计</button>
        </div>
    </div>

    <script>
        // 复制分词算法到测试页面
        function detectLanguageType(text) {
            const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
            const englishChars = (text.match(/[a-zA-Z]/g) || []).length;
            const totalChars = text.length;

            const chineseRatio = chineseChars / totalChars;
            const englishRatio = englishChars / totalChars;

            if (chineseRatio > 0.3) {
                return 'chinese';
            } else if (englishRatio > 0.5) {
                return 'english';
            } else {
                return 'mixed';
            }
        }

        function isInCommonDictionary(word) {
            const commonWords = new Set([
                '因为', '所以', '但是', '然后', '如果', '虽然', '由于', '通过', '根据', '按照',
                '采用', '使用', '利用', '进行', '实现', '完成', '处理', '解决', '提供', '支持',
                '包含', '包括', '具有', '拥有', '存在', '发生', '产生', '形成', '建立', '创建',
                '长期', '短期', '临时', '永久', '当前', '目前', '现在', '将来', '过去', '历史',
                '多个', '单个', '全部', '部分', '整体', '局部', '主要', '次要', '重要', '关键',
                '密钥', '令牌', '凭证', '证书', '权限', '授权', '认证', '验证', '登录', '注册',
                '交互', '互动', '通信', '连接', '网络', '协议', '接口', '端口', '地址', '路径',
                '即可', '可以', '能够', '应该', '必须', '需要', '要求', '建议', '推荐', '选择',
                '基本', '设置', '系统', '功能', '用户', '管理', '配置', '选项', '界面', '操作'
            ]);
            return commonWords.has(word);
        }

        function segmentChineseText(text) {
            const words = [];
            const maxWordLength = 8;
            let position = 0;

            while (position < text.length) {
                let matched = false;

                for (let length = Math.min(maxWordLength, text.length - position); length >= 1; length--) {
                    const candidate = text.substring(position, position + length);

                    // 更宽松的判断逻辑
                    let isValid = false;
                    if (length === 1) {
                        isValid = true; // 单字总是有效
                    } else if (isInCommonDictionary(candidate)) {
                        isValid = true; // 词典中的词汇
                    } else if (length === 2 && /^[\u4e00-\u9fff]{2}$/.test(candidate)) {
                        isValid = true; // 双字中文词汇
                    } else if (length === 3 && /^[\u4e00-\u9fff]{3}$/.test(candidate)) {
                        isValid = true; // 三字中文词汇
                    }

                    if (isValid) {
                        words.push({
                            word: candidate,
                            start: position,
                            end: position + length,
                            type: 'chinese',
                            confidence: isInCommonDictionary(candidate) ? 0.9 : (length === 2 ? 0.7 : 0.6)
                        });

                        position += length;
                        matched = true;
                        break;
                    }
                }

                if (!matched) {
                    const char = text.charAt(position);
                    words.push({
                        word: char,
                        start: position,
                        end: position + 1,
                        type: 'chinese_char',
                        confidence: 0.6
                    });
                    position++;
                }
            }

            return words;
        }

        function testSegmentation() {
            console.clear();
            console.log('🔤 中文分词算法测试');
            console.log('='.repeat(50));

            const testCases = [
                "因为是长期使用且存在多个Token交互的，所以采用密钥管理（KV即可）",
                "基本设置包含系统配置和用户管理功能",
                "由于系统需要处理大量数据，因此建议使用缓存机制",
                "通过API接口可以实现自动化操作"
            ];

            let allResults = [];

            testCases.forEach((testText, index) => {
                console.log(`\n🧪 测试用例 ${index + 1}:`);
                console.log(`原文: "${testText}"`);

                const languageType = detectLanguageType(testText);
                console.log(`语言类型: ${languageType}`);

                // 分离中文和非中文部分
                const segments = testText.split(/([^\u4e00-\u9fff]+)/);
                console.log('分段结果:', segments);

                let allWords = [];
                segments.forEach(segment => {
                    if (!segment) return;

                    if (/[\u4e00-\u9fff]/.test(segment)) {
                        console.log(`处理中文段: "${segment}"`);
                        const chineseWords = segmentChineseText(segment);
                        allWords.push(...chineseWords);
                        console.log('中文分词:', segment, '->', chineseWords.map(w => w.word));
                    } else {
                        console.log(`处理非中文段: "${segment}"`);
                        allWords.push({
                            word: segment,
                            type: 'non-chinese',
                            confidence: 0.8
                        });
                    }
                });

                console.log('分词结果:', allWords.map(w => w.word));
                const resultText = allWords.map(w => `[${w.word}]`).join(' ');
                console.log('格式化结果:', resultText);

                allResults.push({
                    original: testText,
                    result: resultText,
                    words: allWords.map(w => w.word)
                });
            });

            // 显示汇总结果
            let summary = '🔤 分词测试结果汇总:\n\n';
            allResults.forEach((result, index) => {
                summary += `${index + 1}. ${result.original}\n`;
                summary += `   分词: ${result.result}\n\n`;
            });

            alert(summary + '详细信息请查看控制台');
        }
        
        function clearHighlights() {
            const highlights = document.querySelectorAll('.highlight');
            highlights.forEach(el => {
                el.outerHTML = el.innerHTML;
            });
        }
        
        function showStatistics() {
            const textElements = document.querySelectorAll('.chinese-text, .mixed-text, .english-text, .technical-text');
            let totalChars = 0;
            let chineseChars = 0;
            let englishChars = 0;
            
            textElements.forEach(el => {
                const text = el.textContent;
                totalChars += text.length;
                chineseChars += (text.match(/[\u4e00-\u9fff]/g) || []).length;
                englishChars += (text.match(/[a-zA-Z]/g) || []).length;
            });
            
            const stats = `📊 文本统计信息：
            
总字符数：${totalChars}
中文字符：${chineseChars} (${(chineseChars/totalChars*100).toFixed(1)}%)
英文字符：${englishChars} (${(englishChars/totalChars*100).toFixed(1)}%)
其他字符：${totalChars-chineseChars-englishChars} (${((totalChars-chineseChars-englishChars)/totalChars*100).toFixed(1)}%)

测试文本块数：${textElements.length}`;
            
            alert(stats);
        }
        
        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            console.log('🔤 中文分词测试页面已加载');
            console.log('📝 这个页面包含了多种类型的文本用于测试分词算法');
            console.log('🚀 请在安装翻译扩展后访问此页面来测试实际效果');
        });
    </script>
</body>
</html>
