// 插件初始化
let isTranslated = false;
let originalContent = null;
let translatedContent = null;
// 存储原始节点内容的映射，用于恢复
let originalTextMap = new Map();

// 简化的调试日志函数 - 仅在开发模式下输出
function debugLog(message, data = null) {
  // 仅在开发模式下输出日志
  if (false) { // 设为 false 禁用调试日志
    console.log(`[Translator] ${message}`, data || '');
  }
}

// 页面加载完成后检查自动翻译
document.addEventListener('DOMContentLoaded', () => {
  checkAutoTranslate();
});

// 如果页面已经加载完成，立即检查
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', checkAutoTranslate);
} else {
  checkAutoTranslate();
}

// 检查是否需要自动翻译
function checkAutoTranslate() {
  // 获取配置并检查自动翻译设置
  chrome.runtime.sendMessage({ action: 'getConfig' }, (response) => {
    if (response && response.success && response.config.enableAutoTranslate) {
      debugLog('自动翻译已启用，开始翻译页面');
      // 延迟一秒后开始翻译，确保页面完全加载
      setTimeout(() => {
        translatePage();
      }, 1000);
    }
  });
}

// 手动测试翻译功能的按钮 - 已禁用
function addTestButton() {
  // 禁用测试按钮
  return;
  
  /*
  const testButton = document.createElement('button');
  testButton.textContent = '测试翻译功能';
  testButton.style.position = 'fixed';
  testButton.style.top = '50px';
  testButton.style.right = '10px';
  testButton.style.zIndex = '10001';
  testButton.style.padding = '5px 10px';
  testButton.style.backgroundColor = '#4285f4';
  testButton.style.color = 'white';
  testButton.style.border = 'none';
  testButton.style.borderRadius = '4px';
  testButton.style.cursor = 'pointer';
  
  testButton.onclick = () => {
    debugLog('手动触发翻译');
    translatePage();
  };
  
  document.body.appendChild(testButton);
  debugLog('添加了测试按钮');
  */
}

// 不再添加测试按钮
// setTimeout(addTestButton, 1000);

// 监听来自popup或background的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  debugLog('收到消息', message);
  
  if (message.action === 'ping') {
    // 响应ping消息，确认内容脚本已加载
    debugLog('收到ping消息，确认内容脚本已加载');
    sendResponse({ success: true, status: 'content_script_ready' });
  } else if (message.action === 'translate') {
    debugLog('执行翻译操作');
    if (!isTranslated) {
      translatePage();
    } else {
      restoreOriginal();
    }
    sendResponse({ success: true, isTranslated: isTranslated });
  } else if (message.action === 'getStatus') {
    debugLog('获取状态', { isTranslated });
    sendResponse({ isTranslated: isTranslated });
  }
  return true;
});

// 翻译页面
function translatePage() {
  debugLog('开始翻译页面');
  // 保存原始内容
  if (!originalContent) {
    originalContent = document.body.innerHTML;
    debugLog('已保存原始内容');
  }

  // 获取配置
  chrome.runtime.sendMessage({ action: 'getConfig' }, (response) => {
    if (!response || !response.success) {
      debugLog('获取配置失败', response);
      showTranslationError('获取配置失败');
      return;
    }

    const config = response.config;
    debugLog('获取配置成功', config);
    
    // 显示加载中提示
    showTranslationLoading();
    
    // 提取页面文本节点
    const textNodes = extractTextNodes(document.body);
    debugLog(`提取到${textNodes.length}个文本节点`);

    // 按比例选择需要翻译的节点
    const nodesToTranslate = selectNodesToTranslate(textNodes, config);
    debugLog(`将翻译${nodesToTranslate.length}个文本节点（${config.translationPercentage}%）`);
    
    if (nodesToTranslate.length === 0) {
      showTranslationError('没有找到可翻译的文本');
      return;
    }
    
    // 分块处理文本
    const textChunks = splitIntoChunks(nodesToTranslate, config.maxChunkSize);
    debugLog(`文本已分为${textChunks.length}个块进行处理`);
    console.log(`[网页翻译] 文本已分为${textChunks.length}个块进行处理`);
    
    // 显示进度信息
    updateLoadingOverlay(`准备翻译 (0/${textChunks.length})`);
    
    // 使用Promise处理所有翻译任务
    translateChunks(textChunks, config)
      .then(results => {
        // 应用翻译结果到页面
        applyTranslationResults(nodesToTranslate, results)
          .then(appliedCount => {
            debugLog(`翻译完成! 成功应用了${appliedCount}个翻译`);

            isTranslated = true;
            // 不再替换整个页面，而是显示翻译状态指示
            showTranslationStatus("部分内容已翻译");
          })
          .catch(error => {
            debugLog('翻译过程出错', error);
            showTranslationError(error.message || '翻译失败');
          });
      })
      .catch(error => {
        debugLog('翻译过程出错', error);
        showTranslationError(error.message || '翻译失败');
      });
  });
}

// 提取页面中的所有文本节点
function extractTextNodes(node) {
  const textNodes = [];
  
  // 递归函数来遍历DOM树
  function traverse(node) {
    // 如果是文本节点且包含非空文本
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent.trim();
      if (text.length > 0) {
        // 对所有文本都尝试分词，不限制长度
        splitTextIntoPhrases(node, textNodes);
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      // 跳过脚本、样式和隐藏元素
      const style = window.getComputedStyle(node);
      if (node.tagName !== 'SCRIPT' && 
          node.tagName !== 'STYLE' && 
          style.display !== 'none' && 
          style.visibility !== 'hidden') {
        // 遍历子节点
        for (let i = 0; i < node.childNodes.length; i++) {
          traverse(node.childNodes[i]);
        }
      }
    }
  }
  
  // 开始遍历
  traverse(node);
  return textNodes;
}

// 智能单词分割 - 真正的单词级别翻译
function splitTextIntoPhrases(node, textNodes) {
  const text = node.textContent.trim();

  // 对于任何长度的文本都尝试分词
  debugLog(`开始分词处理: "${text}"`);

  // 如果文本为空，跳过
  if (text.length === 0) {
    return;
  }

  // 高级分词算法 - 支持多语言和复杂情况
  const words = advancedWordSegmentation(text);
  debugLog(`分词结果: ${words ? words.length : 0} 个词`, words ? words.map(w => w.word) : []);

  // 如果无法分割成单词，使用整个节点
  if (!words || words.length === 0) {
    debugLog(`分词失败，使用原始节点: "${text}"`);
    textNodes.push({
      node: node,
      text: text,
      length: text.length,
      parentTag: node.parentNode.tagName,
      importance: getNodeImportance(node)
    });
    return;
  }

  // 如果只有一个词且与原文相同，也使用原始节点（但要检查是否真的无法分词）
  if (words.length === 1 && words[0].word.trim() === text.trim()) {
    debugLog(`单词与原文相同，使用原始节点: "${text}"`);
    textNodes.push({
      node: node,
      text: text,
      length: text.length,
      parentTag: node.parentNode.tagName,
      importance: getNodeImportance(node)
    });
    return;
  }

  // 如果分词结果少于2个有效词，也考虑使用原始节点
  const validWords = words.filter(w => w.word.trim().length > 0 && !/^[\s\p{P}]*$/u.test(w.word.trim()));
  if (validWords.length < 2) {
    debugLog(`有效分词数量不足(${validWords.length})，使用原始节点: "${text}"`);
    textNodes.push({
      node: node,
      text: text,
      length: text.length,
      parentTag: node.parentNode.tagName,
      importance: getNodeImportance(node)
    });
    return;
  }

  // 为每个单词创建独立的虚拟节点
  let addedWords = 0;
  words.forEach((wordInfo, index) => {
    // 处理有意义的单词（包括中文字符，排除纯标点和空白）
    const word = wordInfo.word.trim();
    if (word.length > 0 && !/^[\s\p{P}]*$/u.test(word)) {
      // 创建单词节点
      const wordNode = document.createTextNode(word);

      textNodes.push({
        node: wordNode,
        text: word,
        length: word.length,
        parentTag: node.parentNode.tagName,
        importance: getNodeImportance(node),
        isVirtual: true,
        originalNode: node,
        wordIndex: index,
        beforeText: wordInfo.before,
        afterText: wordInfo.after,
        position: wordInfo.position,
        wordType: wordInfo.type,
        confidence: wordInfo.confidence
      });
      addedWords++;
      debugLog(`添加虚拟节点: "${word}" (类型: ${wordInfo.type}, 置信度: ${wordInfo.confidence})`);
    } else {
      debugLog(`跳过无效词: "${word}"`);
    }
  });

  debugLog(`总共添加了 ${addedWords} 个虚拟节点`);

  // 如果没有添加任何有效单词，使用原始节点
  if (!textNodes.some(item => item.originalNode === node && item.isVirtual)) {
    textNodes.push({
      node: node,
      text: text,
      length: text.length,
      parentTag: node.parentNode.tagName,
      importance: getNodeImportance(node)
    });
  }
}

// 高级分词算法 - 优化的中文分词实现
function advancedWordSegmentation(text) {
  // 检测文本主要语言类型
  const languageType = detectLanguageType(text);

  if (languageType === 'chinese') {
    // 使用专门的中文分词算法
    return chineseWordSegmentation(text);
  } else if (languageType === 'english') {
    // 使用英文分词算法
    return englishWordSegmentation(text);
  } else {
    // 混合语言，分别处理
    return mixedLanguageSegmentation(text);
  }
}

// 检测文本主要语言类型
function detectLanguageType(text) {
  const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
  const englishChars = (text.match(/[a-zA-Z]/g) || []).length;
  const totalChars = text.length;

  const chineseRatio = chineseChars / totalChars;
  const englishRatio = englishChars / totalChars;

  if (chineseRatio > 0.3) {
    return 'chinese';
  } else if (englishRatio > 0.5) {
    return 'english';
  } else {
    return 'mixed';
  }
}

// 中文分词算法 - 基于词典和统计的混合方法
function chineseWordSegmentation(text) {
  const words = [];
  let currentPosition = 0;

  // 预处理：分离中文和非中文部分
  const segments = text.split(/([^\u4e00-\u9fff]+)/);

  segments.forEach(segment => {
    if (!segment) return;

    const startPos = text.indexOf(segment, currentPosition);

    if (/[\u4e00-\u9fff]/.test(segment)) {
      // 中文部分使用专门的分词算法
      const chineseWords = segmentChineseText(segment, startPos);
      words.push(...chineseWords);
    } else {
      // 非中文部分使用简单分割
      const nonChineseWords = segmentNonChineseText(segment, startPos);
      words.push(...nonChineseWords);
    }

    currentPosition = startPos + segment.length;
  });

  return words;
}

// 中文文本分词 - 实现基于词典的最大匹配算法
function segmentChineseText(text, basePosition = 0) {
  const words = [];
  const maxWordLength = 8; // 最大词长
  let position = 0;

  debugLog(`开始中文分词: "${text}"`);

  while (position < text.length) {
    let matched = false;

    // 从最长可能的词开始匹配
    for (let length = Math.min(maxWordLength, text.length - position); length >= 1; length--) {
      const candidate = text.substring(position, position + length);

      if (isValidChineseWord(candidate, length)) {
        // 找到有效词汇
        const confidence = calculateWordConfidence(candidate, length);
        words.push({
          word: candidate,
          before: '',
          after: '',
          position: {
            start: basePosition + position,
            end: basePosition + position + length
          },
          type: 'chinese',
          confidence: confidence,
          wordLength: length
        });

        debugLog(`匹配词汇: "${candidate}" (长度:${length}, 置信度:${confidence})`);
        position += length;
        matched = true;
        break;
      }
    }

    // 如果没有匹配到词汇，按单字处理
    if (!matched) {
      const char = text.charAt(position);
      words.push({
        word: char,
        before: '',
        after: '',
        position: {
          start: basePosition + position,
          end: basePosition + position + 1
        },
        type: 'chinese_char',
        confidence: 0.6,
        wordLength: 1
      });
      debugLog(`单字处理: "${char}"`);
      position++;
    }
  }

  debugLog(`中文分词完成: ${words.length} 个词`, words.map(w => w.word));
  return words;
}

// 判断是否为有效的中文词汇（更宽松的判断）
function isValidChineseWord(word, length) {
  // 基于统计和规则的词汇判断

  // 单字总是有效的
  if (length === 1) {
    return true;
  }

  // 检查是否在常用词典中
  if (isInCommonDictionary(word)) {
    return true;
  }

  // 对于双字词，更宽松的判断
  if (length === 2) {
    // 如果都是中文字符，大概率是有效词汇
    if (/^[\u4e00-\u9fff]{2}$/.test(word)) {
      return true;
    }
    return isValidTwoCharWord(word);
  }

  // 对于三字词，也比较宽松
  if (length === 3) {
    // 如果都是中文字符，可能是有效词汇
    if (/^[\u4e00-\u9fff]{3}$/.test(word)) {
      return true;
    }
    return isValidMultiCharWord(word);
  }

  // 对于更长的词，使用更严格的判断
  if (length >= 4) {
    return isValidMultiCharWord(word);
  }

  return false;
}

// 检查是否在常用词典中
function isInCommonDictionary(word) {
  // 扩展的常用中文词汇词典
  const commonWords = new Set([
    // 常用双字词
    '基本', '设置', '系统', '功能', '用户', '管理', '配置', '选项', '界面', '操作',
    '文件', '数据', '信息', '内容', '页面', '网站', '应用', '程序', '软件', '工具',
    '服务', '平台', '技术', '方法', '方式', '类型', '格式', '版本', '更新', '下载',
    '安装', '运行', '启动', '关闭', '打开', '保存', '删除', '修改', '编辑', '查看',
    '搜索', '查找', '过滤', '排序', '分类', '标签', '名称', '标题', '描述', '说明',
    '帮助', '支持', '联系', '关于', '首页', '主页', '导航', '菜单', '按钮', '链接',

    // 新增常用词汇
    '因为', '所以', '但是', '然后', '如果', '虽然', '由于', '通过', '根据', '按照',
    '采用', '使用', '利用', '进行', '实现', '完成', '处理', '解决', '提供', '支持',
    '包含', '包括', '具有', '拥有', '存在', '发生', '产生', '形成', '建立', '创建',
    '长期', '短期', '临时', '永久', '当前', '目前', '现在', '将来', '过去', '历史',
    '多个', '单个', '全部', '部分', '整体', '局部', '主要', '次要', '重要', '关键',
    '密钥', '令牌', '凭证', '证书', '权限', '授权', '认证', '验证', '登录', '注册',
    '交互', '互动', '通信', '连接', '网络', '协议', '接口', '端口', '地址', '路径',
    '即可', '可以', '能够', '应该', '必须', '需要', '要求', '建议', '推荐', '选择',

    // 常用三字词
    '翻译器', '浏览器', '操作系统', '数据库', '用户名', '密码', '登录', '注册', '退出',
    '下一步', '上一步', '确认', '取消', '提交', '重置', '刷新', '加载', '完成',
    '长期使用', '多个交互', '密钥管理', '令牌交互', '系统配置', '用户管理',

    // 常用四字词
    '用户界面', '系统设置', '功能配置', '数据管理', '文件管理', '网络连接',
    '安全设置', '隐私保护', '性能优化', '错误处理', '异常情况', '正常运行',
    '长期使用', '多个令牌', '密钥管理', '交互功能'
  ]);

  return commonWords.has(word);
}

// 判断双字词是否有效
function isValidTwoCharWord(word) {
  const char1 = word.charAt(0);
  const char2 = word.charAt(1);

  // 基于字符类型的组合规律
  const type1 = getChineseCharType(char1);
  const type2 = getChineseCharType(char2);

  // 扩展的有效组合模式
  const validCombinations = [
    ['noun', 'noun'],      // 名词+名词：系统设置、密钥管理
    ['verb', 'noun'],      // 动词+名词：打开文件、采用方式
    ['adj', 'noun'],       // 形容词+名词：基本设置、长期使用
    ['noun', 'verb'],      // 名词+动词：用户登录、系统运行
    ['adv', 'verb'],       // 副词+动词：自动保存、通过处理
    ['verb', 'verb'],      // 动词+动词：编辑修改、存在发生
    ['conj', 'noun'],      // 连词+名词：因为原因、所以结果
    ['conj', 'verb'],      // 连词+动词：因为存在、所以采用
    ['noun', 'adj'],       // 名词+形容词：时间长期、数量多个
    ['verb', 'adj'],       // 动词+形容词：使用方便、处理简单
    ['adj', 'adj'],        // 形容词+形容词：重要关键、简单易用
    ['other', 'noun'],     // 其他+名词：允许更多组合
    ['noun', 'other'],     // 名词+其他：允许更多组合
    ['other', 'verb'],     // 其他+动词：允许更多组合
    ['verb', 'other'],     // 动词+其他：允许更多组合
    ['other', 'other']     // 其他+其他：最宽松的组合
  ];

  return validCombinations.some(([t1, t2]) => type1 === t1 && type2 === t2);
}

// 判断多字词是否有效
function isValidMultiCharWord(word) {
  // 对于3字以上的词，使用更严格的判断
  const length = word.length;

  // 检查是否有重复模式
  if (hasRepeatingPattern(word)) {
    return false;
  }

  // 检查字符类型分布
  const charTypes = word.split('').map(getChineseCharType);
  const typeDistribution = getTypeDistribution(charTypes);

  // 有效的多字词应该有合理的字符类型分布
  return isValidTypeDistribution(typeDistribution, length);
}

// 获取中文字符类型（扩展版）
function getChineseCharType(char) {
  // 扩展的字符分类
  const nouns = '人用户系统文件数据信息内容页面网站应用程序软件工具服务平台技术方法方式类型格式版本密钥令牌凭证证书权限接口端口地址路径网络协议';
  const verbs = '打开关闭保存删除修改编辑查看搜索查找过滤排序分类运行启动安装下载更新采用使用利用进行实现完成处理解决提供支持包含包括具有拥有存在发生产生形成建立创建';
  const adjectives = '基本主要重要常用特殊普通标准高级简单复杂新旧大小长短多个单个全部部分整体局部次要关键临时永久当前目前';
  const adverbs = '自动手动快速慢速立即马上经常偶尔总是从不通过根据按照由于';
  const conjunctions = '因为所以但是然后如果虽然且而或者以及以及并且而且不过';

  if (nouns.includes(char)) return 'noun';
  if (verbs.includes(char)) return 'verb';
  if (adjectives.includes(char)) return 'adj';
  if (adverbs.includes(char)) return 'adv';
  if (conjunctions.includes(char)) return 'conj';

  return 'other';
}

// 检查是否有重复模式
function hasRepeatingPattern(word) {
  const length = word.length;

  // 检查ABAB模式
  if (length === 4) {
    return word.charAt(0) === word.charAt(2) && word.charAt(1) === word.charAt(3);
  }

  // 检查AAA模式
  if (length >= 3) {
    const firstChar = word.charAt(0);
    return word.split('').every(char => char === firstChar);
  }

  return false;
}

// 获取字符类型分布
function getTypeDistribution(charTypes) {
  const distribution = {};
  charTypes.forEach(type => {
    distribution[type] = (distribution[type] || 0) + 1;
  });
  return distribution;
}

// 判断字符类型分布是否有效（更宽松的判断）
function isValidTypeDistribution(distribution, length) {
  const types = Object.keys(distribution);

  // 对于短词汇，更加宽松
  if (length <= 3) {
    return true;
  }

  // 单一类型的词汇在某些情况下是可能的
  if (types.length === 1 && length > 4) {
    // 如果全是'other'类型且长度很长，可能无效
    if (types[0] === 'other' && length > 6) {
      return false;
    }
  }

  // 'other'类型字符过多的词汇可能无效（提高阈值）
  if (distribution.other && distribution.other / length > 0.8) {
    return false;
  }

  return true;
}

// 计算词汇置信度
function calculateWordConfidence(word, length) {
  let confidence = 0.5; // 基础置信度

  // 词典中的词汇置信度更高
  if (isInCommonDictionary(word)) {
    confidence += 0.3;
  }

  // 长度适中的词汇置信度更高
  if (length >= 2 && length <= 4) {
    confidence += 0.2;
  }

  // 字符类型分布合理的词汇置信度更高
  const charTypes = word.split('').map(getChineseCharType);
  const typeDistribution = getTypeDistribution(charTypes);
  if (isValidTypeDistribution(typeDistribution, length)) {
    confidence += 0.1;
  }

  return Math.min(confidence, 1.0);
}

// 处理非中文文本分词
function segmentNonChineseText(text, basePosition = 0) {
  const words = [];
  let position = 0;

  // 使用正则表达式匹配英文单词、数字等
  const patterns = [
    // 英文单词（包括缩写、连字符、所有格）
    {
      regex: /\b[a-zA-Z]+(?:[''][a-zA-Z]+)*(?:-[a-zA-Z]+)*\b/g,
      type: 'english',
      confidence: 0.9
    },
    // 数字（包括小数、百分比）
    {
      regex: /\d+(?:\.\d+)?%?/g,
      type: 'number',
      confidence: 0.7
    },
    // 专有名词（首字母大写的词）
    {
      regex: /\b[A-Z][a-zA-Z]+\b/g,
      type: 'proper_noun',
      confidence: 0.95
    },
    // 技术术语（包含特殊字符的词）
    {
      regex: /\b\w+[._-]\w+\b/g,
      type: 'technical',
      confidence: 0.85
    }
  ];

  // 使用所有模式匹配
  const allMatches = [];

  patterns.forEach(pattern => {
    let match;
    const regex = new RegExp(pattern.regex.source, pattern.regex.flags);

    while ((match = regex.exec(text)) !== null) {
      allMatches.push({
        word: match[0],
        start: match.index,
        end: match.index + match[0].length,
        type: pattern.type,
        confidence: pattern.confidence
      });
    }
  });

  // 按位置排序并去重
  allMatches.sort((a, b) => a.start - b.start);

  // 去重和合并重叠的匹配
  const uniqueMatches = [];
  for (const match of allMatches) {
    const lastMatch = uniqueMatches[uniqueMatches.length - 1];

    if (!lastMatch || match.start >= lastMatch.end) {
      uniqueMatches.push(match);
    } else if (match.confidence > lastMatch.confidence) {
      // 如果新匹配的置信度更高，替换旧的
      uniqueMatches[uniqueMatches.length - 1] = match;
    }
  }

  // 构建最终的单词列表
  uniqueMatches.forEach((match, index) => {
    const beforeText = text.substring(position, match.start);
    const afterText = index < uniqueMatches.length - 1
      ? text.substring(match.end, uniqueMatches[index + 1].start)
      : text.substring(match.end);

    words.push({
      word: match.word,
      before: beforeText,
      after: afterText,
      position: {
        start: basePosition + match.start,
        end: basePosition + match.end
      },
      type: match.type,
      confidence: match.confidence
    });

    position = match.end;
  });

  return words;
}

// 英文分词算法
function englishWordSegmentation(text) {
  return segmentNonChineseText(text, 0);
}

// 混合语言分词算法
function mixedLanguageSegmentation(text) {
  const words = [];
  let currentPosition = 0;

  // 按语言类型分段处理
  const segments = text.split(/([^\u4e00-\u9fff]+|[\u4e00-\u9fff]+)/);

  segments.forEach(segment => {
    if (!segment) return;

    const startPos = text.indexOf(segment, currentPosition);

    if (/[\u4e00-\u9fff]/.test(segment)) {
      // 中文部分
      const chineseWords = segmentChineseText(segment, startPos);
      words.push(...chineseWords);
    } else {
      // 非中文部分
      const nonChineseWords = segmentNonChineseText(segment, startPos);
      words.push(...nonChineseWords);
    }

    currentPosition = startPos + segment.length;
  });

  return words;
}

// 获取节点的重要性分数
function getNodeImportance(node) {
  const parent = node.parentNode;
  if (!parent) return 1;
  
  const tagName = parent.tagName;
  
  // 根据标签类型分配权重
  switch(tagName) {
    case 'H1': return 10;
    case 'H2': return 9;
    case 'H3': return 8;
    case 'H4': return 7;
    case 'H5': return 6;
    case 'H6': return 5;
    case 'P': return 4;
    case 'LI': return 3;
    case 'DIV': return 2;
    default: return 1;
  }
}

// 根据翻译比例选择需要翻译的节点 - 分布式选择算法
function selectNodesToTranslate(textNodes, config) {
  if (!textNodes.length) return [];

  const percentage = config.translationPercentage / 100;
  const mode = config.translationMode || 'distributed';

  // 如果是100%，直接返回所有节点
  if (percentage >= 1) {
    return textNodes;
  }

  // 计算需要翻译的节点数量
  const targetCount = Math.max(1, Math.floor(textNodes.length * percentage));

  switch(mode) {
    case 'distributed':
      // 分布式选择 - 确保翻译的单词均匀分布在整个内容中
      return selectDistributedNodes(textNodes, targetCount);

    case 'random':
      // 随机选择节点
      return shuffleArray(textNodes).slice(0, targetCount);

    case 'top':
      // 从顶部开始选择
      return textNodes.slice(0, targetCount);

    case 'important':
      // 按重要性排序后选择
      return textNodes
        .sort((a, b) => b.importance - a.importance)
        .slice(0, targetCount);

    default:
      return selectDistributedNodes(textNodes, targetCount);
  }
}

// 分布式选择算法 - 确保翻译内容均匀分布
function selectDistributedNodes(textNodes, targetCount) {
  if (targetCount >= textNodes.length) {
    return textNodes;
  }

  // 按照在页面中的位置对节点进行分组
  const groups = groupNodesByPosition(textNodes);

  // 计算每个组应该选择的节点数量
  const selectedNodes = [];

  groups.forEach((group) => {
    // 计算这个组应该选择的节点数量（均匀分布）
    const groupTargetCount = Math.round((targetCount * group.length) / textNodes.length);
    const actualCount = Math.min(groupTargetCount, group.length);

    if (actualCount > 0) {
      // 在组内均匀选择节点
      const step = Math.max(1, Math.floor(group.length / actualCount));
      for (let i = 0; i < actualCount; i++) {
        const nodeIndex = Math.min(i * step, group.length - 1);
        selectedNodes.push(group[nodeIndex]);
      }
    }
  });

  // 如果选择的节点数量不够，从剩余节点中补充
  if (selectedNodes.length < targetCount) {
    const remainingNodes = textNodes.filter(node => !selectedNodes.includes(node));
    const additionalCount = targetCount - selectedNodes.length;
    const additionalNodes = shuffleArray(remainingNodes).slice(0, additionalCount);
    selectedNodes.push(...additionalNodes);
  }

  // 如果选择的节点数量过多，随机移除一些
  if (selectedNodes.length > targetCount) {
    return shuffleArray(selectedNodes).slice(0, targetCount);
  }

  return selectedNodes;
}

// 按位置对节点进行分组
function groupNodesByPosition(textNodes) {
  // 将节点按照在DOM中的位置分成若干组
  const groupSize = Math.max(10, Math.floor(textNodes.length / 10)); // 每组至少10个节点
  const groups = [];

  for (let i = 0; i < textNodes.length; i += groupSize) {
    const group = textNodes.slice(i, i + groupSize);
    if (group.length > 0) {
      groups.push(group);
    }
  }

  return groups;
}

// 辅助函数：随机打乱数组
function shuffleArray(array) {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
}

// 将文本节点分成块，以便批量翻译 - 优化的智能分块算法
function splitIntoChunks(textNodes, maxChunkSize) {
  const chunks = [];
  let currentChunk = [];
  let currentSize = 0;

  // 动态调整块大小，确保更好的并发性能
  const optimalChunkSize = Math.min(maxChunkSize, Math.max(500, maxChunkSize / 2));
  const minChunkSize = Math.min(200, optimalChunkSize / 2);

  debugLog(`分块参数: 最大=${maxChunkSize}, 最优=${optimalChunkSize}, 最小=${minChunkSize}`);

  for (let i = 0; i < textNodes.length; i++) {
    const item = textNodes[i];
    const itemSize = item.text.length;

    // 如果单个项目就超过最大大小，单独成块
    if (itemSize > maxChunkSize) {
      // 先保存当前块（如果有内容）
      if (currentChunk.length > 0) {
        chunks.push(createChunk(currentChunk, currentSize));
        currentChunk = [];
        currentSize = 0;
      }

      // 大项目单独成块
      chunks.push(createChunk([item], itemSize));
      continue;
    }

    // 检查是否需要开始新块
    const wouldExceedOptimal = currentSize + itemSize > optimalChunkSize;
    const wouldExceedMax = currentSize + itemSize > maxChunkSize;
    const hasMinimumContent = currentSize >= minChunkSize;

    if (currentSize > 0 && (wouldExceedMax || (wouldExceedOptimal && hasMinimumContent))) {
      // 保存当前块并开始新块
      chunks.push(createChunk(currentChunk, currentSize));
      currentChunk = [];
      currentSize = 0;
    }

    // 添加到当前块
    currentChunk.push(item);
    currentSize += itemSize;
  }

  // 添加最后一个块
  if (currentChunk.length > 0) {
    chunks.push(createChunk(currentChunk, currentSize));
  }

  // 优化小块：合并过小的块
  const optimizedChunks = mergeSmallChunks(chunks, minChunkSize, maxChunkSize);

  debugLog(`分块完成: 原始${chunks.length}块 -> 优化后${optimizedChunks.length}块`);

  return optimizedChunks;
}

// 创建文本块 - 针对单词级别翻译优化
function createChunk(nodes, size) {
  // 检查是否为单词级别翻译
  const isWordLevel = nodes.some(n => n.isVirtual);

  if (isWordLevel) {
    // 单词级别翻译：每个单词单独处理
    return {
      nodes: nodes,
      text: nodes.map(n => n.text).join('\n'), // 每个单词一行
      size: size,
      wordCount: nodes.length, // 单词数量就是节点数量
      isWordLevel: true,
      translationMode: 'individual_words'
    };
  } else {
    // 传统段落翻译
    return {
      nodes: nodes,
      text: nodes.map(n => n.text).join('\n'),
      size: size,
      wordCount: nodes.reduce((count, n) => count + (n.text.match(/\w+/g) || []).length, 0),
      isWordLevel: false,
      translationMode: 'paragraph'
    };
  }
}

// 合并过小的块以提高效率
function mergeSmallChunks(chunks, minSize, maxSize) {
  if (chunks.length <= 1) return chunks;

  const optimized = [];
  let i = 0;

  while (i < chunks.length) {
    let currentChunk = chunks[i];

    // 如果当前块太小，尝试与下一个块合并
    while (i + 1 < chunks.length &&
           currentChunk.size < minSize &&
           currentChunk.size + chunks[i + 1].size <= maxSize) {
      const nextChunk = chunks[i + 1];

      // 合并块
      currentChunk = {
        nodes: [...currentChunk.nodes, ...nextChunk.nodes],
        text: currentChunk.text + '\n' + nextChunk.text,
        size: currentChunk.size + nextChunk.size,
        wordCount: currentChunk.wordCount + nextChunk.wordCount
      };

      i++; // 跳过已合并的块
    }

    optimized.push(currentChunk);
    i++;
  }

  return optimized;
}

// 更新加载覆盖层的内容 - 使用简洁的智能通知系统
function updateLoadingOverlay(message, details = null) {
  // 优先使用智能通知系统（简洁模式）
  if (window.smartNotifications) {
    const progressMatch = message.match(/\((\d+)\/(\d+)\)/);
    if (progressMatch) {
      const current = parseInt(progressMatch[1]);
      const total = parseInt(progressMatch[2]);
      const percentage = Math.round((current / total) * 100);

      // 更新进度条
      const notifications = document.querySelectorAll('.smart-notification');
      notifications.forEach(notification => {
        const progressBar = notification.querySelector('.notification-progress-bar, .compact-progress-bar');
        if (progressBar) {
          progressBar.style.width = `${percentage}%`;
        }
      });
    }

    // 使用简洁的通知样式
    window.smartNotifications.show({
      id: 'translation-progress',
      type: 'info',
      title: '',
      message: message,
      duration: 0, // 持续显示
      showProgress: true,
      compact: true
    });
  } else {
    // 降级到原有通知系统
    const progressMatch = message.match(/\((\d+)\/(\d+)\)/);
    if (progressMatch) {
      const current = parseInt(progressMatch[1]);
      const total = parseInt(progressMatch[2]);
      const percentage = Math.round((current / total) * 100);

      notificationManager.update('translation-progress', {
        message: message,
        progress: percentage
      });
    } else {
      notificationManager.update('translation-progress', {
        message: message
      });
    }
  }

  debugLog(`更新进度: ${message}${details ? ` - ${details}` : ''}`);
}

// 翻译所有文本块 - 优化的并发处理
async function translateChunks(chunks, config) {
  const results = new Array(chunks.length); // 预分配结果数组
  const concurrentLimit = Math.min(config.concurrentRequests || 3, chunks.length); // 使用配置的并发数
  let completedChunks = 0;

  debugLog(`开始并发翻译，并发数: ${concurrentLimit}，总块数: ${chunks.length}`);

  // 创建进度更新函数（节流）
  let lastProgressUpdate = 0;
  const updateProgress = () => {
    const now = Date.now();
    if (now - lastProgressUpdate > 200) { // 最多每200ms更新一次
      updateLoadingOverlay(`翻译中 (${completedChunks}/${chunks.length})`);
      lastProgressUpdate = now;
    }
  };

  // 创建翻译任务
  const translateTask = async (chunk, index) => {
    const startTime = Date.now();
    try {
      debugLog(`开始翻译块 ${index + 1}/${chunks.length}`);

      const result = await translateChunk(chunk, config);

      // 原子操作更新结果
      results[index] = {
        index: index,
        success: true,
        translations: result.translations,
        duration: Date.now() - startTime
      };

      // 原子操作更新计数器
      completedChunks++;
      updateProgress();

      debugLog(`块 ${index + 1} 翻译完成，耗时 ${Date.now() - startTime}ms，包含 ${result.translations.length} 个翻译项`);

      return result;
    } catch (error) {
      debugLog(`块 ${index + 1} 翻译失败`, error);

      results[index] = {
        index: index,
        success: false,
        error: error.message,
        duration: Date.now() - startTime
      };

      completedChunks++;
      updateProgress();

      // 不抛出错误，让其他任务继续执行
      return null;
    }
  };

  // 使用 Promise.allSettled 进行真正的并发处理
  if (concurrentLimit >= chunks.length) {
    // 如果并发数大于等于块数，直接并发所有任务
    debugLog('使用全并发模式');
    const promises = chunks.map((chunk, index) => translateTask(chunk, index));
    await Promise.allSettled(promises);
  } else {
    // 使用批次并发处理
    debugLog(`使用批次并发模式，每批 ${concurrentLimit} 个任务`);

    for (let i = 0; i < chunks.length; i += concurrentLimit) {
      const batch = chunks.slice(i, i + concurrentLimit);
      const batchPromises = batch.map((chunk, batchIndex) =>
        translateTask(chunk, i + batchIndex)
      );

      debugLog(`执行批次 ${Math.floor(i / concurrentLimit) + 1}，包含 ${batch.length} 个任务`);
      await Promise.allSettled(batchPromises);
    }
  }

  // 统计结果
  const successCount = results.filter(r => r && r.success).length;
  const failureCount = results.filter(r => r && !r.success).length;
  const totalDuration = results.reduce((sum, r) => sum + (r?.duration || 0), 0);
  const avgDuration = totalDuration / results.length;

  debugLog('并发翻译完成', {
    total: chunks.length,
    success: successCount,
    failure: failureCount,
    totalDuration: `${totalDuration}ms`,
    avgDuration: `${avgDuration.toFixed(2)}ms`,
    concurrency: concurrentLimit
  });

  return results.filter(r => r !== null); // 过滤掉null结果
}

// 商业化级别的通知系统管理器
class NotificationManager {
  constructor() {
    this.notifications = new Map();
    this.maxNotifications = 3;
    this.defaultDuration = 5000;
    this.zIndexBase = 10000;
    this.position = 'top-right';
    this.spacing = 16;
    this.initialized = false;

    this.init();
  }

  init() {
    if (this.initialized) return;

    // 添加全局样式
    this.injectStyles();

    // 创建通知容器
    this.createContainer();

    this.initialized = true;
  }

  injectStyles() {
    if (document.getElementById('notification-manager-styles')) return;

    const style = document.createElement('style');
    style.id = 'notification-manager-styles';
    style.textContent = `
      .notification-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: ${this.zIndexBase};
        pointer-events: none;
        display: flex;
        flex-direction: column;
        gap: ${this.spacing}px;
        max-width: 400px;
        width: 100%;
      }

      .notification-item {
        pointer-events: auto;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 16px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
        transform: translateX(100%);
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
        will-change: transform, opacity;
      }

      .notification-item.show {
        transform: translateX(0);
        opacity: 1;
      }

      .notification-item.hide {
        transform: translateX(100%);
        opacity: 0;
        margin-bottom: -100px;
      }

      .notification-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 20px;
        background: linear-gradient(135deg, var(--notification-color-start), var(--notification-color-end));
        color: white;
        position: relative;
        cursor: pointer;
        user-select: none;
      }

      .notification-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.1);
        opacity: 0;
        transition: opacity 0.2s;
      }

      .notification-header:hover::before {
        opacity: 1;
      }

      .notification-left {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;
        min-width: 0;
      }

      .notification-icon {
        font-size: 20px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.2);
      }

      .notification-title {
        font-size: 14px;
        font-weight: 600;
        line-height: 1.4;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .notification-controls {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-shrink: 0;
      }

      .notification-btn {
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 6px;
        border-radius: 6px;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
        font-size: 12px;
      }

      .notification-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.1);
      }

      .notification-content {
        padding: 0 20px 16px 20px;
        background: rgba(255, 255, 255, 0.95);
        color: #333;
        max-height: 200px;
        opacity: 1;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;
      }

      .notification-item.collapsed .notification-content {
        max-height: 0;
        padding: 0 20px;
        opacity: 0;
      }

      .notification-progress {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 3px;
        background: rgba(255, 255, 255, 0.3);
        transition: width 0.1s linear;
      }

      .notification-message {
        font-size: 13px;
        line-height: 1.4;
        margin-bottom: 12px;
        color: #666;
      }

      .notification-actions {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }

      .notification-action-btn {
        background: rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.1);
        color: #333;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        gap: 6px;
      }

      .notification-action-btn:hover {
        background: rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
      }

      .notification-action-btn.primary {
        background: #4285f4;
        color: white;
        border-color: #4285f4;
      }

      .notification-action-btn.primary:hover {
        background: #3367d6;
      }

      /* 不同类型的颜色主题 */
      .notification-item.loading {
        --notification-color-start: #4285f4;
        --notification-color-end: #34a853;
      }

      .notification-item.success {
        --notification-color-start: #4caf50;
        --notification-color-end: #2e7d32;
      }

      .notification-item.error {
        --notification-color-start: #f44336;
        --notification-color-end: #d32f2f;
      }

      .notification-item.warning {
        --notification-color-start: #ff9800;
        --notification-color-end: #f57c00;
      }

      .notification-item.info {
        --notification-color-start: #2196f3;
        --notification-color-end: #1976d2;
      }

      /* 响应式设计 */
      @media (max-width: 480px) {
        .notification-container {
          left: 16px;
          right: 16px;
          top: 16px;
          max-width: none;
        }

        .notification-item {
          transform: translateY(-100%);
        }

        .notification-item.show {
          transform: translateY(0);
        }

        .notification-item.hide {
          transform: translateY(-100%);
          margin-bottom: 0;
        }
      }

      /* 动画关键帧 */
      @keyframes notification-shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
      }

      @keyframes notification-pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
      }

      .notification-item.shake {
        animation: notification-shake 0.5s ease-in-out;
      }

      .notification-item.pulse {
        animation: notification-pulse 0.6s ease-in-out;
      }
    `;

    document.head.appendChild(style);
  }

  createContainer() {
    if (document.getElementById('notification-container')) return;

    const container = document.createElement('div');
    container.id = 'notification-container';
    container.className = 'notification-container';
    document.body.appendChild(container);
  }

  show(options) {
    const id = options.id || `notification-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    // 如果已存在相同ID的通知，更新它
    if (this.notifications.has(id)) {
      return this.update(id, options);
    }

    // 如果通知数量超过限制，移除最旧的
    if (this.notifications.size >= this.maxNotifications) {
      const oldestId = this.notifications.keys().next().value;
      this.hide(oldestId);
    }

    const notification = this.createNotification(id, options);
    this.notifications.set(id, notification);

    // 添加到容器
    const container = document.getElementById('notification-container');
    container.appendChild(notification.element);

    // 触发显示动画
    requestAnimationFrame(() => {
      notification.element.classList.add('show');
    });

    // 设置自动关闭
    if (options.duration !== false) {
      const duration = options.duration || this.defaultDuration;
      notification.autoCloseTimer = setTimeout(() => {
        this.hide(id);
      }, duration);
    }

    return id;
  }

  createNotification(id, options) {
    const {
      type = 'info',
      title = '通知',
      message = '',
      icon = null,
      actions = [],
      collapsible = true,
      closable = true,
      progress = null
    } = options;

    // 创建通知元素
    const element = document.createElement('div');
    element.className = `notification-item ${type}`;
    element.setAttribute('data-notification-id', id);

    // 创建头部
    const header = document.createElement('div');
    header.className = 'notification-header';

    const leftSection = document.createElement('div');
    leftSection.className = 'notification-left';

    // 图标
    const iconElement = document.createElement('div');
    iconElement.className = 'notification-icon';
    iconElement.innerHTML = icon || this.getDefaultIcon(type);

    // 标题
    const titleElement = document.createElement('div');
    titleElement.className = 'notification-title';
    titleElement.textContent = title;

    leftSection.appendChild(iconElement);
    leftSection.appendChild(titleElement);

    // 控制按钮
    const controls = document.createElement('div');
    controls.className = 'notification-controls';

    if (collapsible) {
      const collapseBtn = document.createElement('button');
      collapseBtn.className = 'notification-btn collapse-btn';
      collapseBtn.innerHTML = '▼';
      collapseBtn.setAttribute('aria-label', '收缩/展开');
      controls.appendChild(collapseBtn);
    }

    if (closable) {
      const closeBtn = document.createElement('button');
      closeBtn.className = 'notification-btn close-btn';
      closeBtn.innerHTML = '✕';
      closeBtn.setAttribute('aria-label', '关闭');
      controls.appendChild(closeBtn);
    }

    header.appendChild(leftSection);
    header.appendChild(controls);

    // 创建内容区域
    const content = document.createElement('div');
    content.className = 'notification-content';

    if (message) {
      const messageElement = document.createElement('div');
      messageElement.className = 'notification-message';
      messageElement.textContent = message;
      content.appendChild(messageElement);
    }

    // 操作按钮
    if (actions.length > 0) {
      const actionsContainer = document.createElement('div');
      actionsContainer.className = 'notification-actions';

      actions.forEach(action => {
        const actionBtn = document.createElement('button');
        actionBtn.className = `notification-action-btn ${action.type || ''}`;
        actionBtn.innerHTML = `${action.icon || ''} ${action.label}`;
        actionBtn.onclick = () => {
          if (action.handler) action.handler();
          if (action.closeOnClick !== false) this.hide(id);
        };
        actionsContainer.appendChild(actionBtn);
      });

      content.appendChild(actionsContainer);
    }

    // 进度条
    let progressElement = null;
    if (progress !== null) {
      progressElement = document.createElement('div');
      progressElement.className = 'notification-progress';
      progressElement.style.width = `${progress}%`;
      header.appendChild(progressElement);
    }

    element.appendChild(header);
    element.appendChild(content);

    // 事件监听器
    let isCollapsed = false;

    // 收缩/展开功能
    const toggleCollapse = () => {
      isCollapsed = !isCollapsed;
      element.classList.toggle('collapsed', isCollapsed);
      const collapseBtn = element.querySelector('.collapse-btn');
      if (collapseBtn) {
        collapseBtn.innerHTML = isCollapsed ? '▲' : '▼';
      }
    };

    if (collapsible) {
      const collapseBtn = element.querySelector('.collapse-btn');
      collapseBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        toggleCollapse();
      });

      header.addEventListener('click', (e) => {
        if (!e.target.closest('.notification-controls')) {
          toggleCollapse();
        }
      });
    }

    // 关闭功能
    if (closable) {
      const closeBtn = element.querySelector('.close-btn');
      closeBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        this.hide(id);
      });
    }

    return {
      element,
      progressElement,
      isCollapsed: () => isCollapsed,
      toggleCollapse,
      updateProgress: (value) => {
        if (progressElement) {
          progressElement.style.width = `${value}%`;
        }
      },
      updateMessage: (newMessage) => {
        const messageEl = element.querySelector('.notification-message');
        if (messageEl) {
          messageEl.textContent = newMessage;
        }
      },
      updateTitle: (newTitle) => {
        const titleEl = element.querySelector('.notification-title');
        if (titleEl) {
          titleEl.textContent = newTitle;
        }
      }
    };
  }

  getDefaultIcon(type) {
    const icons = {
      loading: '⏳',
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };
    return icons[type] || icons.info;
  }

  update(id, options) {
    const notification = this.notifications.get(id);
    if (!notification) return null;

    if (options.title) notification.updateTitle(options.title);
    if (options.message) notification.updateMessage(options.message);
    if (options.progress !== undefined) notification.updateProgress(options.progress);

    return id;
  }

  hide(id) {
    const notification = this.notifications.get(id);
    if (!notification) return;

    // 清除自动关闭定时器
    if (notification.autoCloseTimer) {
      clearTimeout(notification.autoCloseTimer);
    }

    // 添加隐藏动画
    notification.element.classList.add('hide');

    // 动画完成后移除元素
    setTimeout(() => {
      if (notification.element.parentNode) {
        notification.element.parentNode.removeChild(notification.element);
      }
      this.notifications.delete(id);
    }, 400);
  }

  hideAll() {
    this.notifications.forEach((_, id) => {
      this.hide(id);
    });
  }

  // 特殊效果
  shake(id) {
    const notification = this.notifications.get(id);
    if (notification) {
      notification.element.classList.add('shake');
      setTimeout(() => {
        notification.element.classList.remove('shake');
      }, 500);
    }
  }

  pulse(id) {
    const notification = this.notifications.get(id);
    if (notification) {
      notification.element.classList.add('pulse');
      setTimeout(() => {
        notification.element.classList.remove('pulse');
      }, 600);
    }
  }
}

// 创建全局通知管理器实例
const notificationManager = new NotificationManager();

// 翻译单个文本块 - 优化版本，支持重试和更好的错误处理
async function translateChunk(chunk, config, retryCount = 0) {
  const maxRetries = 2;
  const baseTimeout = 30000; // 基础超时30秒
  const timeout = baseTimeout + (retryCount * 10000); // 重试时增加超时时间

  return new Promise((resolve, reject) => {
    let timeoutId = null;
    let isResolved = false;

    // 设置超时
    timeoutId = setTimeout(() => {
      if (!isResolved) {
        isResolved = true;
        const errorMsg = `翻译请求超时 (${timeout/1000}秒)${retryCount > 0 ? ` - 重试 ${retryCount}/${maxRetries}` : ''}`;

        if (retryCount < maxRetries) {
          debugLog(`${errorMsg}，准备重试...`);
          // 递归重试
          translateChunk(chunk, config, retryCount + 1)
            .then(resolve)
            .catch(reject);
        } else {
          reject(new Error(errorMsg));
        }
      }
    }, timeout);

    const startTime = Date.now();

    chrome.runtime.sendMessage(
      {
        action: 'translateContent',
        content: chunk.text,
        sourceLanguage: 'auto',
        targetLanguage: config.defaultTargetLanguage || 'zh-CN',
        delimiters: true,
        chunkInfo: {
          size: chunk.size,
          wordCount: chunk.wordCount,
          nodeCount: chunk.nodes.length
        }
      },
      response => {
        // 清除超时
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }

        if (isResolved) return; // 已经处理过了
        isResolved = true;

        const duration = Date.now() - startTime;

        if (chrome.runtime.lastError) {
          const errorMsg = `通信错误: ${chrome.runtime.lastError.message}`;

          if (retryCount < maxRetries) {
            debugLog(`${errorMsg}，准备重试 ${retryCount + 1}/${maxRetries}...`);
            // 延迟重试
            setTimeout(() => {
              translateChunk(chunk, config, retryCount + 1)
                .then(resolve)
                .catch(reject);
            }, 1000 * (retryCount + 1)); // 递增延迟
          } else {
            reject(new Error(errorMsg));
          }
          return;
        }

        if (response && response.success) {
          try {
            // 解析翻译结果，分配给各个文本节点
            const translations = parseTranslations(chunk, response.translatedContent);
            debugLog(`块翻译成功: ${translations.length}条结果，耗时${duration}ms`);

            resolve({
              translations: translations,
              metadata: {
                ...response.metadata,
                duration: duration,
                retryCount: retryCount,
                chunkSize: chunk.size,
                wordCount: chunk.wordCount
              }
            });
          } catch (parseError) {
            debugLog('解析翻译结果失败', parseError);

            if (retryCount < maxRetries) {
              debugLog(`解析失败，准备重试 ${retryCount + 1}/${maxRetries}...`);
              setTimeout(() => {
                translateChunk(chunk, config, retryCount + 1)
                  .then(resolve)
                  .catch(reject);
              }, 1000 * (retryCount + 1));
            } else {
              reject(new Error(`解析翻译结果失败: ${parseError.message}`));
            }
          }
        } else {
          const errorMsg = response ? response.error : '翻译失败';

          if (retryCount < maxRetries && !errorMsg.includes('API密钥')) {
            debugLog(`翻译失败: ${errorMsg}，准备重试 ${retryCount + 1}/${maxRetries}...`);
            setTimeout(() => {
              translateChunk(chunk, config, retryCount + 1)
                .then(resolve)
                .catch(reject);
            }, 1000 * (retryCount + 1));
          } else {
            reject(new Error(errorMsg));
          }
        }
      }
    );
  });
}

// 解析翻译结果，将其分配回对应的文本节点
function parseTranslations(chunk, translatedContent) {
  debugLog(`收到翻译内容: ${translatedContent.substring(0, 100)}${translatedContent.length > 100 ? '...' : ''}`);

  // 将翻译结果按行分割，对应到原始文本节点
  const translatedLines = translatedContent.split('\n');
  const originalNodes = chunk.nodes;
  
  // 确保我们有足够的翻译行
  const translations = [];
  for (let i = 0; i < originalNodes.length; i++) {
    // 如果对应的翻译行不存在，使用原始文本
    const translatedText = i < translatedLines.length ? translatedLines[i] : originalNodes[i].text;
    
    // 只有翻译和原文不同时才添加
    if (translatedText.trim() !== originalNodes[i].text.trim()) {
      translations.push({
        original: originalNodes[i],
        translated: translatedText
      });
    }
  }
  
  return translations;
}

// 应用翻译结果到页面
function applyTranslationResults(originalNodes, results) {
  // 使用Promise包装配置获取过程
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({ action: 'getConfig' }, (response) => {
      if (!response || !response.success) {
        debugLog('获取配置失败', response);
        reject(new Error('获取配置失败'));
        return;
      }
      
      const config = response.config;
      // 使用配置中的翻译样式
      const translationStyle = config.translationStyle || 'replace';
      const underlineColor = config.underlineColor || '#4285f4';

      debugLog('应用翻译样式', { style: translationStyle, color: underlineColor });
      
      const translationMap = new Map();
      // 收集所有虚拟节点的原始节点及其翻译结果
      const virtualNodeTranslations = new Map();
      
      // 从所有结果中提取翻译映射
      results.forEach(result => {
        if (result.success && result.translations) {
          result.translations.forEach(item => {
            const originalNode = item.original;
            // 检查是否是虚拟节点
            if (originalNode.isVirtual && originalNode.originalNode) {
              // 为虚拟节点的原始节点收集翻译
              if (!virtualNodeTranslations.has(originalNode.originalNode)) {
                virtualNodeTranslations.set(originalNode.originalNode, []);
              }
              virtualNodeTranslations.get(originalNode.originalNode).push({
                original: originalNode.text,
                translated: item.translated
              });
            } else {
              // 常规节点直接保存原始文本和翻译
              if (!originalTextMap.has(originalNode.node)) {
                originalTextMap.set(originalNode.node, originalNode.node.textContent);
              }
              translationMap.set(originalNode.node, item.translated);
            }
          });
        }
      });
      
      // 处理虚拟节点的原始节点
      virtualNodeTranslations.forEach((translations, originalNode) => {
        // 保存原始内容
        if (!originalTextMap.has(originalNode)) {
          originalTextMap.set(originalNode, originalNode.textContent);
        }
        
        // 获取原始文本
        const fullText = originalNode.textContent;
        let newText = fullText;
        
        // 应用所有翻译，按原文从长到短排序以避免替换冲突
        translations.sort((a, b) => b.original.length - a.original.length);
        
        translations.forEach(item => {
          const { original, translated } = item;

          // 跳过相同的文本
          if (original.trim() === translated.trim()) {
            return;
          }

          // 根据翻译样式应用不同的显示方式
          let replacementText;
          switch (translationStyle) {
            case 'underline':
              // 下划线+括号原文样式
              replacementText = `<span class="translated-word" data-original="${original}" data-translated="${translated}" style="text-decoration: underline; text-decoration-color: ${underlineColor}; cursor: help;" title="原文: ${original}">${translated}</span> (${original})`;
              break;
            case 'tooltip':
              // 鼠标悬停显示原文样式
              replacementText = `<span class="translated-word" data-original="${original}" data-translated="${translated}" style="cursor: help; border-bottom: 1px dotted ${underlineColor};" title="原文: ${original}">${translated}</span>`;
              break;
            case 'replace':
            default:
              // 直接替换原文
              replacementText = translated;
              break;
          }

          newText = newText.replace(original, replacementText);
        });
        
        // 设置新内容
        if (newText !== fullText) {
          try {
            // 根据翻译样式选择设置方式
            if (translationStyle === 'replace') {
              // 直接替换时使用textContent
              originalNode.textContent = newText;
            } else {
              // 带样式时使用innerHTML
              originalNode.innerHTML = newText;
            }
            // 添加标记
            originalNode.dataset.translated = 'true';
            debugLog(`成功替换虚拟节点: ${fullText.substring(0, 30)} => ${newText.substring(0, 30)}`);
          } catch (e) {
            debugLog(`替换虚拟节点失败`, e);
          }
        }
      });
      
      // 应用翻译到常规节点
      let appliedCount = 0;
      originalNodes.forEach(item => {
        // 跳过虚拟节点，因为它们已经被处理
        if (item.isVirtual) {
          return;
        }
        
        const translatedText = translationMap.get(item.node);
        if (translatedText) {
          const originalText = item.node.textContent.trim();
          
          // 如果翻译和原文相同，不做特殊处理
          if (translatedText.trim() === originalText) {
            return;
          }
          
          try {
            // 根据翻译样式应用不同的显示方式
            switch (translationStyle) {
              case 'underline':
                // 下划线+括号原文样式
                const underlineSpan = document.createElement('span');
                underlineSpan.className = 'translated-word';
                underlineSpan.setAttribute('data-original', originalText);
                underlineSpan.setAttribute('data-translated', translatedText);
                underlineSpan.style.textDecoration = 'underline';
                underlineSpan.style.textDecorationColor = underlineColor;
                underlineSpan.style.cursor = 'help';
                underlineSpan.title = `原文: ${originalText}`;
                underlineSpan.textContent = translatedText;

                const originalTextSpan = document.createTextNode(` (${originalText})`);

                // 创建包装容器
                const wrapper = document.createElement('span');
                wrapper.appendChild(underlineSpan);
                wrapper.appendChild(originalTextSpan);

                // 替换原节点
                item.node.parentNode.replaceChild(wrapper, item.node);
                wrapper.dataset.translated = 'true';
                break;

              case 'tooltip':
                // 鼠标悬停显示原文样式
                const tooltipSpan = document.createElement('span');
                tooltipSpan.className = 'translated-word';
                tooltipSpan.setAttribute('data-original', originalText);
                tooltipSpan.setAttribute('data-translated', translatedText);
                tooltipSpan.style.cursor = 'help';
                tooltipSpan.style.borderBottom = `1px dotted ${underlineColor}`;
                tooltipSpan.title = `原文: ${originalText}`;
                tooltipSpan.textContent = translatedText;

                // 替换原节点
                item.node.parentNode.replaceChild(tooltipSpan, item.node);
                tooltipSpan.dataset.translated = 'true';
                break;

              case 'replace':
              default:
                // 直接替换原文
                item.node.textContent = translatedText;
                item.node.dataset.translated = 'true';
                break;
            }

            appliedCount++;
            debugLog(`成功替换节点 (${translationStyle}): ${originalText.substring(0, 30)} => ${translatedText.substring(0, 30)}`);
          } catch (e) {
            debugLog(`替换节点失败`, e);
          }
        }
      });
      
      // 计算虚拟节点的数量
      const virtualCount = virtualNodeTranslations.size;
      
      debugLog(`应用了${appliedCount}个常规翻译和${virtualCount}个句子级翻译`, { style: translationStyle });

      // 如果启用了悬停功能，添加事件监听器
      if (config.enableHoverUsage) {
        addHoverUsageListeners();
      }

      resolve(appliedCount + virtualCount);
    });
  });
}

// 添加悬停显示用法的事件监听器
function addHoverUsageListeners() {
  const translatedWords = document.querySelectorAll('.translated-word');

  translatedWords.forEach(word => {
    word.addEventListener('mouseenter', showWordUsage);
    word.addEventListener('mouseleave', hideWordUsage);
  });
}

// 显示单词用法
function showWordUsage(event) {
  const element = event.target;
  const original = element.getAttribute('data-original');
  const translated = element.getAttribute('data-translated');

  if (!original || !translated) return;

  // 创建用法提示框
  const usageTooltip = document.createElement('div');
  usageTooltip.id = 'word-usage-tooltip';
  usageTooltip.style.cssText = `
    position: absolute;
    background: #333;
    color: white;
    padding: 12px;
    border-radius: 8px;
    font-size: 14px;
    max-width: 300px;
    z-index: 10000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
  `;

  // 生成用法内容
  const usageContent = generateWordUsage(original, translated);
  usageTooltip.innerHTML = usageContent;

  // 定位提示框
  document.body.appendChild(usageTooltip);

  const rect = element.getBoundingClientRect();
  const tooltipRect = usageTooltip.getBoundingClientRect();

  let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
  let top = rect.top - tooltipRect.height - 10;

  // 确保提示框在视窗内
  if (left < 10) left = 10;
  if (left + tooltipRect.width > window.innerWidth - 10) {
    left = window.innerWidth - tooltipRect.width - 10;
  }
  if (top < 10) {
    top = rect.bottom + 10;
  }

  usageTooltip.style.left = left + 'px';
  usageTooltip.style.top = top + 'px';

  // 显示动画
  setTimeout(() => {
    usageTooltip.style.opacity = '1';
  }, 10);
}

// 隐藏单词用法
function hideWordUsage() {
  const tooltip = document.getElementById('word-usage-tooltip');
  if (tooltip) {
    tooltip.style.opacity = '0';
    setTimeout(() => {
      if (tooltip.parentNode) {
        tooltip.parentNode.removeChild(tooltip);
      }
    }, 300);
  }
}

// 生成单词用法内容
function generateWordUsage(original, translated) {
  // 这里可以集成真实的词典API，现在先提供模拟内容
  const usageExamples = getWordUsageExamples(original);

  return `
    <div style="margin-bottom: 8px;">
      <strong>${original}</strong> → <strong style="color: #4CAF50;">${translated}</strong>
    </div>
    <div style="font-size: 12px; color: #ccc; margin-bottom: 6px;">用法示例:</div>
    <div style="font-size: 12px; line-height: 1.4;">
      ${usageExamples}
    </div>
  `;
}

// 获取单词用法示例（模拟数据，可以后续集成真实词典API）
function getWordUsageExamples(word) {
  const examples = {
    'hello': '• Hello, how are you? (你好，你好吗？)<br>• Say hello to your family. (向你的家人问好)',
    'world': '• The world is beautiful. (世界很美丽)<br>• Travel around the world. (环游世界)',
    'computer': '• I use a computer for work. (我用电脑工作)<br>• Computer technology is advancing. (计算机技术在进步)',
    'translate': '• Please translate this text. (请翻译这段文字)<br>• Google Translate is useful. (谷歌翻译很有用)',
    'language': '• English is a global language. (英语是全球语言)<br>• Learn a new language. (学习一门新语言)',
    'system': '• The system is working properly. (系统运行正常)<br>• Operating system updates. (操作系统更新)',
    'function': '• This function is useful. (这个功能很有用)<br>• Mathematical functions. (数学函数)',
    'example': '• For example, this works. (例如，这样有效)<br>• Set a good example. (树立好榜样)',
    'content': '• The content is interesting. (内容很有趣)<br>• Website content management. (网站内容管理)',
    'feature': '• New feature released. (新功能发布)<br>• Key features include... (主要功能包括...)'
  };

  const lowerWord = word.toLowerCase();

  if (examples[lowerWord]) {
    return examples[lowerWord];
  }

  // 默认示例
  return `• This is an example of "${word}". (这是"${word}"的一个例子)<br>• "${word}" can be used in different contexts. ("${word}"可以在不同语境中使用)`;
}

// 显示翻译加载中 - 使用新的通知系统
function showTranslationLoading() {
  debugLog('显示翻译加载中');

  // 移除旧的覆盖层
  const existingOverlay = document.getElementById('translation-overlay');
  if (existingOverlay) {
    existingOverlay.remove();
  }

  // 使用新的通知系统
  return notificationManager.show({
    id: 'translation-progress',
    type: 'loading',
    title: '🌐 翻译进行中',
    message: '正在准备翻译引擎...',
    icon: '⚡',
    duration: false, // 不自动关闭
    progress: 0,
    actions: [
      {
        label: '取消',
        icon: '⏹️',
        handler: () => {
          // 取消翻译逻辑
          debugLog('用户取消翻译');
          hideTranslationLoading();
        }
      }
    ]
  });
}

// 隐藏翻译加载框
function hideTranslationLoading() {
  // 隐藏新的通知系统
  notificationManager.hide('translation-progress');

  // 兼容旧的覆盖层
  const overlay = document.getElementById('translation-overlay');
  if (overlay) {
    overlay.style.transform = 'translateX(100%)';
    overlay.style.opacity = '0';
    setTimeout(() => {
      if (overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
      }
    }, 300);
  }
}

// 恢复原始内容
function restoreOriginal() {
  debugLog('恢复原始内容');
  
  if (originalTextMap.size > 0) {
    // 恢复所有被修改的文本节点
    originalTextMap.forEach((originalText, node) => {
      try {
        // 检查节点是否仍在DOM中
        if (!document.contains(node)) {
          return;
        }
        
        // 直接恢复原始文本，不再区分节点类型
        node.textContent = originalText;
        
        // 移除翻译标记
        if (node.dataset) {
          delete node.dataset.translated;
          delete node.dataset.highlighted;
        }
      } catch (e) {
        console.error('恢复节点内容时出错', e);
      }
    });
    
    // 清空映射
    originalTextMap.clear();
    
    // 移除状态指示器
    const statusIndicator = document.getElementById('translation-status');
    if (statusIndicator) {
      statusIndicator.remove();
    }
    
    isTranslated = false;
    debugLog('已恢复所有原始内容');
  } else if (originalContent) {
    // 作为备份，如果没有文本映射，则恢复整个body内容
    // 这种情况不应该发生，但作为安全措施保留
    document.body.innerHTML = originalContent;
    isTranslated = false;
    debugLog('使用备份方法恢复原始内容');
  }
}

// 显示翻译状态指示器 - 使用简洁的智能通知系统
function showTranslationStatus(message) {
  debugLog('显示翻译状态', message);

  // 优先使用智能通知系统
  if (window.smartNotifications) {
    // 移除进度通知
    window.smartNotifications.remove('translation-progress');

    // 显示简洁的完成通知
    window.smartNotifications.success('', message || '翻译完成', {
      duration: 2000,
      compact: true
    });
  } else {
    // 降级到原有通知系统
    notificationManager.hide('translation-progress');
    notificationManager.show({
      id: 'translation-success',
      type: 'success',
      title: '✅ 翻译完成',
      message: message,
      icon: '🎉',
      duration: 3000 // 缩短显示时间
    });
  }

  // 兼容旧的覆盖层处理
  const loadingOverlay = document.getElementById('translation-overlay');
  if (loadingOverlay) {
    loadingOverlay.style.transform = 'translateX(100%)';
    loadingOverlay.style.opacity = '0';
    setTimeout(() => loadingOverlay.remove(), 300);
  }

  const existingIndicator = document.getElementById('translation-status');
  if (existingIndicator) {
    existingIndicator.remove();
  }

  // 收集并显示翻译统计信息
  setTimeout(updateTranslationStats, 500);
}

// 突出显示已翻译内容
function highlightTranslatedContent() {
  // 寻找所有带有translated标记的元素
  const translatedElements = document.querySelectorAll('[data-translated="true"]');
  
  if (translatedElements.length === 0) {
    alert('未找到已翻译的内容，请尝试先进行翻译');
    return;
  }
  
  // 为每个翻译元素添加高亮效果
  translatedElements.forEach(el => {
    // 检查是否已有高亮
    if (el.dataset.highlighted === 'true') {
      // 移除高亮
      el.style.backgroundColor = '';
      el.style.outline = '';
      delete el.dataset.highlighted;
    } else {
      // 添加高亮
      el.style.backgroundColor = 'rgba(255, 235, 59, 0.3)';
      el.style.outline = '2px solid #ffc107';
      el.dataset.highlighted = 'true';
      
      // 确保元素可见（滚动到视图中）
      el.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  });
}

// 更新翻译统计信息
function updateTranslationStats() {
  const statsElement = document.getElementById('translation-stats');
  if (!statsElement) return;
  
  // 计算已翻译的元素数量
  const translatedElements = document.querySelectorAll('[data-translated="true"]');
  const translatedCount = translatedElements.length;
  
  // 查找带有样式的翻译元素数量
  const underlineElements = document.querySelectorAll('span[style*="text-decoration: underline"]').length;
  const tooltipElements = document.querySelectorAll('span[style*="border-bottom"][style*="dotted"]').length;
  const styledElements = underlineElements + tooltipElements;
  
  // 获取配置
  chrome.runtime.sendMessage({ action: 'getConfig' }, (response) => {
    if (!response || !response.success) return;
    
    const config = response.config;
    statsElement.textContent = `已翻译${translatedCount}个元素 • 样式: ${config.translationStyle || 'replace'} • 比例: ${config.translationPercentage || 100}%`;

    debugLog('翻译统计', {
      translatedCount,
      style: config.translationStyle || 'replace',
      percentage: config.translationPercentage || 100,
      styledElements
    });
  });
}

// 显示翻译错误 - 使用简洁的智能通知系统
function showTranslationError(errorMessage) {
  debugLog('显示翻译错误', errorMessage);

  // 优先使用智能通知系统
  if (window.smartNotifications) {
    // 移除进度通知
    window.smartNotifications.remove('translation-progress');

    // 显示简洁的错误通知
    window.smartNotifications.error('', errorMessage || '翻译失败', {
      duration: 3000,
      compact: true
    });
  } else {
    // 降级到原有通知系统
    notificationManager.hide('translation-progress');
    notificationManager.show({
      id: 'translation-error',
      type: 'error',
      title: '❌ 翻译失败',
      message: errorMessage,
      icon: '⚠️',
      duration: 4000 // 缩短显示时间
    });
  }

  // 兼容旧的覆盖层处理
  const loadingOverlay = document.getElementById('translation-overlay');
  if (loadingOverlay) {
    loadingOverlay.style.transform = 'translateX(100%)';
    loadingOverlay.style.opacity = '0';
    setTimeout(() => loadingOverlay.remove(), 300);
  }


}